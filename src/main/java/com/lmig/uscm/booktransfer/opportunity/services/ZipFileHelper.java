package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.domain.creation.FileContent;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

public final class ZipFileHelper {

	private ZipFileHelper() {
	}

	/**
	 * Determines if a file is a compressed file or not
	 *
	 * @param f
	 * @return
	 */
	public static boolean isArchive(final File f) {
		int fileSignature = 0;
		try (RandomAccessFile raf = new RandomAccessFile(f, "r")) {
			fileSignature = raf.readInt();
		} catch (IOException e) {
			return false;
		}
		return fileSignature == 0x504B0304 || fileSignature == 0x504B0506 || fileSignature == 0x504B0708;
	}

	/**
	 * Determines if a file is a hidden file created from Mac or Windows compressions
	 * @param fileName
	 * @return
	 */
	public static boolean isHiddenMetaFile(final String fileName) {
		return StringUtils.containsIgnoreCase(fileName, "__MACOSX") ||
				StringUtils.containsIgnoreCase(fileName, "DS_Store");
	}

	/**
	 * Compresses files provided in argument filePaths at the location provided in argument zipOutPath
	 *
	 * @param zipOutPath
	 * @param filePaths
	 * @throws IOException
	 */
	public static void createZipFile(String zipOutPath, String... filePaths) throws IOException {
		List<String> srcFiles = new ArrayList(Arrays.asList(filePaths));
		FileOutputStream fos = new FileOutputStream(zipOutPath);
		ZipOutputStream zipOut = new ZipOutputStream(fos);
		for (String srcFile : srcFiles) {
			File fileToZip = new File(srcFile);
			FileInputStream fis = new FileInputStream(fileToZip);
			ZipEntry zipEntry = new ZipEntry(fileToZip.getName());
			zipOut.putNextEntry(zipEntry);

			byte[] bytes = new byte[1024];
			int length;
			while ((length = fis.read(bytes)) >= 0) {
				zipOut.write(bytes, 0, length);
			}
			fis.close();
		}
		zipOut.close();
		fos.close();
	}

	/**
	 * Extracts contents from passed file (whether it is compressed or not) into a List
	 *
	 * @param file
	 * @return
	 * throws IOException
	 */
	public static List<FileContent> readFileContents(final File file) throws IOException {
		List<FileContent> files = new ArrayList<>();
		if (isArchive(file)) {
			return unzipFile(file);
		} else {
			files.add(new FileContent(file.getName(), new String(new FileInputStream(file).readAllBytes())));
			return files;
		}
	}

	/**
	 * Extracts contents from a compressed file into a List
	 *
	 * @param zippedFile
	 * @return
	 * throws IOException
	 */
	public static List<FileContent> unzipFile(final File zippedFile) throws IOException {
		List<FileContent> files = new ArrayList<>();

		ZipInputStream zis = new ZipInputStream(new FileInputStream(zippedFile));

		// Get the zip file name without extension to use as folder prefix if needed
		String zipFileName = getFileNameWithoutExtension(zippedFile.getName());

		ZipEntry zipEntry = zis.getNextEntry();
		byte[] buffer = new byte[1024];
		while (zipEntry != null) {
			// Skip directories and hidden files
			if (!zipEntry.isDirectory() && !isHiddenMetaFile(zipEntry.getName())) {
				// write file content
				ByteArrayOutputStream baos =
						new ByteArrayOutputStream();
				BufferedOutputStream bos =
						new BufferedOutputStream(baos, buffer.length);

				int len;
				while ((len = zis.read(buffer, 0, buffer.length)) > 0) {
					bos.write(buffer, 0, len);
				}
				bos.flush();
				bos.close();

				// Ensure file name includes the zip folder structure
				String fileName = ensureZipFolderStructure(zipEntry.getName(), zipFileName);
				files.add(new FileContent(fileName, baos.toString()));
				baos.close();
			}
			zipEntry = zis.getNextEntry();
		}
		zis.closeEntry();
		zis.close();

		return files;
	}

	/**
	 * Ensures that the file name includes the zip folder structure.
	 * If the entry name already contains a folder path, returns it as-is.
	 * If the entry name is just a file name, prepends the zip file name as a folder.
	 *
	 * @param entryName the name from the zip entry
	 * @param zipFileName the name of the zip file (without extension)
	 * @return the file name with proper folder structure
	 */
	private static String ensureZipFolderStructure(String entryName, String zipFileName) {
		// If the entry already contains a folder path (has '/'), return as-is
		if (entryName.contains("/")) {
			return entryName;
		}

		// If it's just a file name, prepend the zip file name as folder
		return zipFileName + "/" + entryName;
	}

	/**
	 * Gets the file name without extension
	 *
	 * @param fileName the full file name
	 * @return the file name without extension
	 */
	private static String getFileNameWithoutExtension(String fileName) {
		int lastDotIndex = fileName.lastIndexOf('.');
		if (lastDotIndex > 0) {
			return fileName.substring(0, lastDotIndex);
		}
		return fileName;
	}
}
