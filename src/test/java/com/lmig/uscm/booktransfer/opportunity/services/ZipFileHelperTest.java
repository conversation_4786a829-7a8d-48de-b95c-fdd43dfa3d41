package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.domain.creation.FileContent;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.io.File;
import java.lang.reflect.Method;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ZipFileHelperTest {

	private static final String ZIP_OUT_PATH = "src/test/resources/xml/multiCompressed.zip";

	@AfterEach
	public void cleanup() {
		File zipFile = new File(ZIP_OUT_PATH);
		zipFile.delete();
	}

	@Test
	void testIsArchive() throws Exception {
		String normalFilePath = "src/test/resources/xml/auto12Vehs.xml";
		String archiveFilePath = "src/test/resources/xml/testArchiveDeep.zip";

		String archiveWithoutExtension = "src/test/resources/xml/testArchive";

		ZipFileHelper.createZipFile(archiveWithoutExtension, normalFilePath);

		File archiveFile = new File(archiveFilePath);
		File normalFile = new File(normalFilePath);
		File noExtensionFile = new File(archiveWithoutExtension);

		assertFalse(ZipFileHelper.isArchive(normalFile));
		assertTrue(ZipFileHelper.isArchive(archiveFile));
		assertTrue(ZipFileHelper.isArchive(noExtensionFile));

		noExtensionFile.delete();
	}

	@Test
	void testUnzipDeepAndShallowFiles() throws Exception {
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String file2 = "src/test/resources/xml/home.xml";
		String archiveDeep = "src/test/resources/xml/testArchiveDeep.zip";
		String archiveShallow = "src/test/resources/xml/testArchiveShallow.zip";

		String file1Content = XmlHelper.getFileContentFromPath(file1);
		String file2Content = XmlHelper.getFileContentFromPath(file2);

		File zipFile = new File(archiveShallow);

		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		assertEquals(file1Content, unzippedFiles.get(0).getContent());
		assertEquals("testArchiveShallow/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals(file2Content, unzippedFiles.get(1).getContent());
		assertEquals("testArchiveShallow/home.xml", unzippedFiles.get(1).getFileName());

		zipFile = new File(archiveDeep);

		unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		assertEquals(file1Content, unzippedFiles.get(0).getContent());
		assertEquals("SomeFolder/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals(file2Content, unzippedFiles.get(1).getContent());
		assertEquals("SomeFolder/home.xml", unzippedFiles.get(1).getFileName());
	}

	@Test
	void testZipAndUnzipFile() throws Exception {
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String file2 = "src/test/resources/xml/home.xml";
		String[] filePaths = new String[]{file1, file2};
		ZipFileHelper.createZipFile(ZIP_OUT_PATH, filePaths);

		String file1Content = XmlHelper.getFileContentFromPath(file1);
		String file2Content = XmlHelper.getFileContentFromPath(file2);

		File zipFile = new File(ZIP_OUT_PATH);

		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		assertEquals(file1Content, unzippedFiles.get(0).getContent());
		assertEquals("multiCompressed/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals(file2Content, unzippedFiles.get(1).getContent());
		assertEquals("multiCompressed/home.xml", unzippedFiles.get(1).getFileName());
	}

	@Test
	void testReadFileContents_ZipOrNotZip() throws Exception {
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String file2 = "src/test/resources/xml/home.xml";
		String[] filePaths = new String[]{file1, file2};
		ZipFileHelper.createZipFile(ZIP_OUT_PATH, filePaths);

		String file1Content = XmlHelper.getFileContentFromPath(file1);
		String file2Content = XmlHelper.getFileContentFromPath(file2);

		File zipFile = new File(ZIP_OUT_PATH);

		List<FileContent> archiveFileContents = ZipFileHelper.readFileContents(zipFile);

		assertEquals(file1Content, archiveFileContents.get(0).getContent());
		assertEquals("multiCompressed/auto12Vehs.xml", archiveFileContents.get(0).getFileName());
		assertEquals(file2Content, archiveFileContents.get(1).getContent());
		assertEquals("multiCompressed/home.xml", archiveFileContents.get(1).getFileName());

		File notZipFile = new File(file1);

		List<FileContent> fileContents = ZipFileHelper.readFileContents(notZipFile);

		assertEquals(file1Content, fileContents.get(0).getContent());
		assertEquals("auto12Vehs.xml", fileContents.get(0).getFileName());
	}

	@Test
	void testUnzipFile_WithoutFolderStructure_ShouldAddZipNameAsFolder() throws Exception {

		String archiveShallow = "src/test/resources/xml/testArchiveShallow.zip";
		File zipFile = new File(archiveShallow);

		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		// Verify that files without folder structure get the zip name as folder prefix
		assertEquals("testArchiveShallow/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals("testArchiveShallow/home.xml", unzippedFiles.get(1).getFileName());
	}

	@Test
	void testUnzipFile_WithFolderStructure_ShouldPreserveOriginalPath() throws Exception {
		// Test that files with existing folder structure are preserved
		String archiveDeep = "src/test/resources/xml/testArchiveDeep.zip";
		File zipFile = new File(archiveDeep);

		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		// Verify that files with existing folder structure are preserved as-is
		assertEquals("SomeFolder/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals("SomeFolder/home.xml", unzippedFiles.get(1).getFileName());
	}

	@Test
	void testUnzipFile_WithoutFileStructureScenario() throws Exception {
		// Create a test zip file that simulates the "Main Street America.zip" scenario
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String file2 = "src/test/resources/xml/home.xml";
		String[] filePaths = new String[]{file1, file2};
		String mainStreetZipPath = "src/test/resources/xml/Main Street America.zip";

		ZipFileHelper.createZipFile(mainStreetZipPath, filePaths);

		File zipFile = new File(mainStreetZipPath);
		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		// Verify that files get the zip name as folder prefix (simulating the expected behavior)
		assertEquals("Main Street America/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals("Main Street America/home.xml", unzippedFiles.get(1).getFileName());

		// Cleanup
		zipFile.delete();
	}

	/**
	 * Test cases for the getFileNameWithoutExtension method to verify edge cases
	 * are handled correctly after the fix for filenames ending with dots.
	 */
	@ParameterizedTest
	@CsvSource({
		"'document.txt', 'document'",           // Normal case
		"'file.pdf', 'file'",                  // Normal case
		"'archive.tar.gz', 'archive.tar'",     // Multiple extensions
		"'filename.', 'filename.'",            // Edge case: ends with dot (should return original)
		"'.hidden', '.hidden'",                // Edge case: starts with dot (should return original)
		"'.gitignore', '.gitignore'",          // Edge case: hidden file (should return original)
		"'noextension', 'noextension'",        // No extension
		"'file..txt', 'file.'",                // Double dot before extension
		"'a.b', 'a'",                          // Single character before and after dot
		"'test.backup.', 'test.backup.'",      // Multiple dots with trailing dot
		"'', ''",                              // Empty string
		"'.', '.'",                            // Single dot
		"'..', '..'",                          // Double dot
		"'file.xml', 'file'"                   // XML file (common in this codebase)
	})
	void testGetFileNameWithoutExtension(String input, String expected) throws Exception {
		// Use reflection to access the private method
		Method method = ZipFileHelper.class.getDeclaredMethod("getFileNameWithoutExtension", String.class);
		method.setAccessible(true);

		String result = (String) method.invoke(null, input);
		assertEquals(expected, result,
			String.format("getFileNameWithoutExtension('%s') should return '%s' but returned '%s'",
				input, expected, result));
	}

	/**
	 * Test the integration of getFileNameWithoutExtension with the unzipFile method
	 * to ensure the fix works correctly in the actual usage context.
	 */
	@Test
	void testUnzipFile_WithFileNameEndingInDot_ShouldPreserveDot() throws Exception {
		// Create a test zip file with a name ending in dot
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String[] filePaths = new String[]{file1};
		String zipPathWithDot = "src/test/resources/xml/testfile..zip";

		ZipFileHelper.createZipFile(zipPathWithDot, filePaths);

		File zipFile = new File(zipPathWithDot);
		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		// The folder name should preserve the trailing dot since getFileNameWithoutExtension
		// should return "testfile." for "testfile..zip"
		assertEquals("testfile./auto12Vehs.xml", unzippedFiles.get(0).getFileName());

		// Cleanup
		zipFile.delete();
	}

	/**
	 * Test the integration with hidden files (files starting with dot)
	 */
	@Test
	void testUnzipFile_WithHiddenFileName_ShouldPreserveDot() throws Exception {
		// Create a test zip file with a hidden file name
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String[] filePaths = new String[]{file1};
		String hiddenZipPath = "src/test/resources/xml/.hidden.zip";

		ZipFileHelper.createZipFile(hiddenZipPath, filePaths);

		File zipFile = new File(hiddenZipPath);
		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		// The folder name should be ".hidden" since getFileNameWithoutExtension
		// should return ".hidden" for ".hidden.zip"
		assertEquals(".hidden/auto12Vehs.xml", unzippedFiles.get(0).getFileName());

		// Cleanup
		zipFile.delete();
	}
}
